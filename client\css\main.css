/* ===== CSS CUSTOM PROPERTIES FOR THEMING ===== */
:root {
  /* Light Theme Colors */
  --bg-primary: #f4f6fa;
  --bg-secondary: #fff;
  --bg-tertiary: #f5f8fa;
  --bg-accent: #e3f2fd;
  --bg-hover: #f5f5f5;
  --bg-selected: #e3f2fd;

  --text-primary: #212529;
  --text-secondary: #666;
  --text-tertiary: #444;
  --text-accent: #1976d2;
  --text-muted: #7c88a1;

  --border-primary: #e2e6ea;
  --border-secondary: #e0e0e0;
  --border-accent: #1976d2;

  --shadow-light: #1976d210;
  --shadow-medium: #1976d222;
  --shadow-heavy: rgba(0,0,0,0.15);

  --success: #34a853;
  --warning: #ffb300;
  --error: #f44336;
  --info: #2196f3;
}

/* Dark Theme Colors */
[data-theme="dark"] {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2d2d2d;
  --bg-accent: #1a237e;
  --bg-hover: #333;
  --bg-selected: #1a237e;

  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-tertiary: #ccc;
  --text-accent: #64b5f6;
  --text-muted: #888;

  --border-primary: #404040;
  --border-secondary: #555;
  --border-accent: #64b5f6;

  --shadow-light: rgba(0,0,0,0.3);
  --shadow-medium: rgba(0,0,0,0.4);
  --shadow-heavy: rgba(0,0,0,0.6);

  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --info: #2196f3;
}

body {
  background: var(--bg-primary);
  font-family: 'Google Sans', Arial, sans-serif;
  margin: 0; padding: 0;
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.main-content {
  max-width: 980px;
  margin: 28px auto 0;
  background: var(--bg-secondary);
  border-radius: 18px;
  box-shadow: 0 2px 28px var(--shadow-light);
  padding: 32px 40px 36px 40px;
  position: relative;
  min-height: 540px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* HEADER */
.drive-header {
  display: flex; align-items: center; justify-content: space-between;
  background: var(--bg-secondary);
  height: 57px; box-shadow: 0 1px 8px var(--shadow-medium);
  padding: 0 28px; font-size: 20px; font-weight: 700;
  z-index: 10; letter-spacing: -0.5px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title {
  font-size: 21px;
  color: var(--text-accent);
  font-family: 'Google Sans', Arial, sans-serif;
  font-weight: 700;
  transition: color 0.3s ease;
}

/* Theme Toggle Button */
.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  font-size: 20px;
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-accent);
  transform: scale(1.1);
}

.theme-toggle:active {
  transform: scale(0.95);
}

.theme-toggle .mdi {
  transition: transform 0.3s ease;
}

/* Theme toggle animation */
.theme-toggle.switching .mdi {
  transform: rotate(180deg);
}

/* View Toggle Button */
.view-toggle-container {
  display: flex;
  align-items: center;
}

.btn-view-toggle {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  padding: 9px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  min-width: 42px;
  height: 42px;
}

.btn-view-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-accent);
  border-color: var(--border-accent);
  transform: scale(1.05);
}

.btn-view-toggle:active {
  transform: scale(0.95);
}

.btn-view-toggle.active {
  background: var(--text-accent);
  color: var(--bg-secondary);
  border-color: var(--text-accent);
}

.btn-view-toggle .mdi {
  transition: transform 0.3s ease;
}

/* View toggle animation */
.btn-view-toggle.switching .mdi {
  transform: scale(0.8) rotate(90deg);
}

/* ===== GRID VIEW STYLES ===== */

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 8px 0;
  transition: opacity 0.3s ease;
}

.file-grid-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.file-grid-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-accent);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.file-grid-item.selected {
  background: var(--bg-selected);
  border-color: var(--border-accent);
  box-shadow: 0 0 0 2px var(--border-accent);
}

.file-grid-icon {
  font-size: 48px;
  margin-bottom: 8px;
  transition: transform 0.3s ease;
}

.file-grid-item:hover .file-grid-icon {
  transform: scale(1.1);
}

.file-grid-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  word-break: break-word;
  line-height: 1.3;
  max-height: 2.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  transition: color 0.3s ease;
}

.file-grid-meta {
  font-size: 11px;
  color: var(--text-secondary);
  margin-top: auto;
  transition: color 0.3s ease;
}

.file-grid-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-grid-item:hover .file-grid-actions {
  opacity: 1;
}

.file-grid-action {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 6px;
  padding: 4px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.file-grid-action:hover {
  background: var(--bg-hover);
  color: var(--text-accent);
  border-color: var(--border-accent);
}

/* Grid responsive breakpoints */
@media (max-width: 768px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }

  .file-grid-item {
    min-height: 120px;
    padding: 12px 8px;
  }

  .file-grid-icon {
    font-size: 36px;
  }

  .file-grid-name {
    font-size: 12px;
  }

  .file-grid-meta {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .file-grid-item {
    min-height: 100px;
    padding: 8px 6px;
  }

  .file-grid-icon {
    font-size: 32px;
    margin-bottom: 6px;
  }

  .file-grid-name {
    font-size: 11px;
  }
}

/* TOOLBAR */
.toolbar {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 18px;
  flex-wrap: wrap;
  justify-content: space-between;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-primary {
  flex: 1;
  min-width: 0; /* Allow shrinking */
}

.toolbar-secondary {
  flex-shrink: 0; /* Don't shrink secondary actions */
}

.upload-label, .btn {
  display: flex; align-items: center; cursor: pointer;
  padding: 9px 19px; border-radius: 8px; background: var(--bg-tertiary);
  font-size: 16px; border: 1px solid var(--border-primary); box-shadow: 0 1px 4px #0001;
  transition: background 0.17s, box-shadow .23s, color .15s;
  color: var(--text-accent);
  font-weight: 500;
  outline: none;
  gap: 9px;
}

.upload-label input { display: none; }

.btn-create, .btn-primary { 
  background: #1976d2; color: #fff; border: none;
}

.btn-create:hover, .btn-primary:hover { 
  background: #0c56a5;
}

.btn-cancel { 
  background: #f3f3f3; color: #555; border: 1px solid #ddd;
}

.btn-cancel:hover { 
  background: #eee; color: #d32f2f;
}

/* Upload button styles */
.upload-label.upload-file {
  background: #34a853 !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #34a85328;
}

.upload-label.upload-file:hover {
  background: #289946 !important;
  color: #fff !important;
}

.upload-label.upload-folder {
  background: #ffb300 !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #ffb3001e;
}

.upload-label.upload-folder:hover {
  background: #d39e00 !important;
  color: #fff !important;
}

.upload-label.upload-file .mdi,
.upload-label.upload-folder .mdi {
  color: #fff !important;
}

/* SEARCH BOX */
.search-container {
  flex: 1;
  max-width: 400px;
  margin-left: 20px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: 25px;
  padding: 0 15px;
  transition: border-color 0.3s, box-shadow 0.3s, background-color 0.3s;
}

.search-box:focus-within {
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 10px;
  font-size: 20px;
  transition: color 0.3s ease;
}

#searchInput {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 0;
  font-size: 16px;
  background: transparent;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

#searchInput::placeholder {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.search-clear {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background 0.2s, color 0.3s ease;
}

.search-clear:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.btn-back-search {
  background: #e3f2fd;
  border: 1px solid #1976d2;
  color: #1976d2;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-back-search:hover {
  background: #1976d2;
  color: white;
}

/* SORT CONTROLS */
.sort-container {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.sort-dropdown {
  position: relative;
}

.btn-sort {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
  min-width: 140px;
  justify-content: space-between;
}

.btn-sort:hover {
  background: #f5f5f5;
  border-color: #ccc;
  color: #333;
}

.btn-sort:focus {
  outline: none;
  box-shadow: 0 0 0 2px #2196f341;
}

.sort-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  margin-top: 4px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s;
  min-width: 200px;
}

.sort-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.sort-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background 0.2s;
  position: relative;
}

.sort-option:hover {
  background: #f5f5f5;
}

.sort-option:first-child {
  border-radius: 8px 8px 0 0;
}

.sort-option:last-child {
  border-radius: 0 0 8px 8px;
}

.sort-option .mdi:first-child {
  color: #666;
  font-size: 18px;
  width: 20px;
}

.sort-option span:nth-child(2) {
  flex: 1;
}

.sort-check {
  color: #4caf50;
  font-size: 16px;
  display: none;
}

.sort-divider {
  height: 1px;
  background: #e0e0e0;
  margin: 4px 0;
}

.sort-reset {
  color: #ff9800;
}

.sort-reset:hover {
  background: #fff3e0;
}

.sort-reset .mdi:first-child {
  color: #ff9800;
}

/* MULTI-SELECT STYLES */

.checkbox-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.file-checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: #1976d2;
}

.checkbox-label {
  display: none; /* Hide the custom label for now, using native checkbox */
}

/* Bulk Actions Toolbar */
.bulk-actions-toolbar {
  background: #e3f2fd;
  border: 1px solid #1976d2;
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px 0;
  display: none;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.bulk-actions-toolbar.show {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.bulk-actions-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.bulk-selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1976d2;
  font-weight: 600;
  font-size: 15px;
}

.bulk-selection-info .mdi {
  font-size: 20px;
}

.bulk-actions {
  display: flex;
  gap: 8px;
}

.btn-bulk-action {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-bulk-download {
  background: #4caf50;
  color: white;
}

.btn-bulk-download:hover:not(:disabled) {
  background: #45a049;
  box-shadow: 0 2px 8px #4caf5041;
}

.btn-bulk-delete {
  background: #f44336;
  color: white;
}

.btn-bulk-delete:hover:not(:disabled) {
  background: #da190b;
  box-shadow: 0 2px 8px #f4433641;
}

.btn-bulk-clear {
  background: #666;
  color: white;
}

.btn-bulk-clear:hover {
  background: #555;
  box-shadow: 0 2px 8px #66666641;
}

.btn-bulk-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-bulk-action .mdi {
  font-size: 16px;
}

/* Selection mode styling */
.selection-mode .file-list tbody tr {
  cursor: pointer;
}

.selection-mode .file-list tbody tr:hover {
  background: #e8f4f8 !important;
}

.selection-mode .file-list tbody tr:has(.file-checkbox:checked) {
  background: #e3f2fd !important;
  border-left: 3px solid #1976d2;
}

/* Checkbox styling for different states */
.file-checkbox:indeterminate {
  opacity: 0.8;
}

/* Mobile responsive for bulk actions */
@media (max-width: 768px) {
  .bulk-actions-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .bulk-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .btn-bulk-action {
    font-size: 13px;
    padding: 6px 10px;
  }
  
  .bulk-selection-info {
    font-size: 14px;
  }
}

/* BREADCRUMBS */
.breadcrumbs {
  margin-bottom: 14px;
  font-size: 16px;
  color: #444;
  display: flex;
  gap: 7px;
  align-items: center;
  flex-wrap: wrap;
  user-select: text;
}

.breadcrumbs span {
  cursor: pointer;
  color: #1976d2;
  font-weight: 500;
  padding: 0 2px;
}

.breadcrumbs .breadcrumb-link {
  cursor: pointer;
  color: #1976d2;
  font-weight: 500;
  padding: 0 2px;
  transition: color 0.2s;
}

.breadcrumbs .breadcrumb-link:hover {
  color: #004ba0;
  text-decoration: underline;
}

.breadcrumbs .mdi-chevron-right {
  color: #7c88a1; font-size: 19px;
  padding: 0 2px;
}

.breadcrumbs .breadcrumb-current {
  font-weight: bold; color: #212529; 
  background: #eaf3ff; border-radius: 5px; 
  padding: 1px 7px;
  pointer-events: none; cursor: default;
}

/* DRAG & DROP */
.drop-area {
  border: 2.5px dashed #4285f4;
  border-radius: 14px;
  background: #e3f0fd;
  text-align: center;
  color: #1976d2;
  font-size: 20px;
  padding: 36px;
  display: none;
  margin-bottom: 20px;
  font-weight: 500;
  transition: background 0.25s, box-shadow 0.25s;
  box-shadow: 0 2px 18px #4285f44d;
  align-items: center;
  justify-content: center;
}

.drop-area-show {
  background: #e7eefa;
  border-color: #34a853;
  color: #1976d2;
  box-shadow: 0 8px 28px #34a85344;
  animation: droparea-pop .21s;
}

@keyframes droparea-pop {
  0% { transform: scale(0.97);}
  90% { transform: scale(1.025);}
  100% { transform: scale(1);}
}

/* FILE TABLE */
.table-wrapper {
  width: 100%;
  overflow-x: auto;
  border-radius: 13px;
  box-shadow: 0 1px 16px var(--shadow-light);
}

.file-list {
  width: 100%;
  min-width: 600px; /* Minimum width to prevent cramping */
  border-collapse: separate;
  border-spacing: 0;
  background: var(--bg-secondary);
  border-radius: 13px;
  overflow: hidden;
  table-layout: fixed;
}

.file-list th, .file-list td {
  font-size: 15.8px;
  padding: 14px 12px;
  vertical-align: middle;
  background: none;
  transition: background 0.18s;
}

.file-list th {
  color: var(--text-accent);
  font-weight: 700;
  background: var(--bg-tertiary);
  border-bottom: 2px solid var(--border-primary);
}

.file-list tbody tr {
  background: var(--bg-secondary);
  border-bottom: 1.5px solid var(--border-primary);
}

.file-list tbody tr:nth-child(odd) {
  background: var(--bg-tertiary);
}

.file-list tbody tr:hover {
  background: #e9f3ff;
  box-shadow: 0 2px 8px #99c2f355;
  z-index: 1;
  position: relative;
}

.file-list tbody tr.highlighted {
  background: #fff3e0 !important;
  box-shadow: 0 0 10px #ff9800;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0%, 100% { background: #fff3e0; }
  50% { background: #ffe0b2; }
}

.file-list td {
  color: #23395d;
  line-height: 1.4;
  word-break: break-all;
  border: none;
}

.file-list tr:first-child th:first-child {
  border-top-left-radius: 13px;
}

.file-list tr:first-child th:last-child {
  border-top-right-radius: 13px;
}

.file-list tr:last-child td:first-child {
  border-bottom-left-radius: 13px;
}

.file-list tr:last-child td:last-child {
  border-bottom-right-radius: 13px;
}

/* Column sizes */
.col-checkbox {
  width: 40px;
  min-width: 40px;
  padding: 8px 4px;
  text-align: center;
}

.col-name {
  width: 45%;
  min-width: 160px;
  font-weight: 600;
}

.col-time {
  width: 20%;
  min-width: 80px;
  color: #6b7da8;
  font-size: 14.2px;
  font-weight: 500;
  text-align: center;
}

.col-size {
  width: 15%;
  min-width: 65px;
  text-align: center;
  font-size: 15px;
  color: #3a6e54;
}

.col-action,
.file-list td:last-child, 
.file-list th:last-child {
  min-width: 120px;
  width: 15%;
  text-align: center;
  font-size: 17px;
  padding-right: 5px;
}

/* File links and icons */
.file-list .folder-link, 
.file-list .file-link {
  font-weight: 600;
  font-size: 16.2px;
  color: #1976d2;
  transition: color 0.13s;
  text-decoration: none;
}

.file-list .folder-link:hover, 
.file-list .file-link:hover {
  color: #004ba0;
  text-decoration: underline;
}

.file-icon { 
  font-size: 22px; 
  margin-right: 8px; 
  vertical-align: middle; 
}

/* Rename button */
.btn-rename {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  border-radius: 4px;
  font-size: 16px;
  opacity: 0;
  transition: all 0.2s;
}

.file-list tbody tr:hover .btn-rename {
  opacity: 1;
}

.btn-rename:hover {
  background: #e3f2fd;
  color: #1976d2;
}

/* Action buttons */
.action-group {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120px;
  gap: 2px;
}

.btn-action {
  border: none;
  background: none;
  cursor: pointer;
  font-size: 24px;
  border-radius: 50%;
  width: 38px; height: 38px;
  display: flex; 
  align-items: center; 
  justify-content: center;
  transition: background 0.18s, color .17s, box-shadow .18s;
  margin: 0 1px;
  padding: 0;
}

.btn-download {
  color: #43b755;
}

.btn-download:hover {
  background: #e6f6eb;
  color: #27ae60;
  box-shadow: 0 2px 8px #27ae6041;
}

.btn-preview {
  color: #2196f3;
}

.btn-preview:hover {
  background: #e3f2fd;
  color: #0d47a1;
  box-shadow: 0 2px 8px #2196f341;
}

.btn-locate {
  color: #ff9800;
}

.btn-locate:hover {
  background: #fff3e0;
  color: #f57c00;
  box-shadow: 0 2px 8px #ff980041;
}

.btn-delete {
  color: #ec4747;
}

.btn-delete:hover {
  background: #feeaea;
  color: #d32f2f;
  box-shadow: 0 2px 8px #d32f2f41;
}

/* Search result styles */
.search-result-row .file-path {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.search-result-row mark {
  background: #ffeb3b;
  padding: 1px 2px;
  border-radius: 2px;
}

/* MODAL STYLES */
.modal-bg {
  position: fixed; left:0; top:0; right:0; bottom:0;
  background:rgba(27,42,71,0.17);
  z-index: 199;
  display: flex; align-items: center; justify-content: center;
  animation: modalFadeIn .22s;
}

@keyframes modalFadeIn { 
  from{ opacity:0; } 
  to{opacity:1;} 
}

.modal {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 7px 32px #1976d233;
  padding: 27px 36px 21px 36px;
  min-width: 340px; max-width: 98vw;
  display: flex; flex-direction: column; gap: 16px;
  animation: popin .18s;
}

@keyframes popin { 
  from{transform:scale(0.94);} 
  to{transform:scale(1);} 
}

.modal-title {
  font-size: 20px; font-weight: 700; color: #1976d2; 
  margin-bottom: 7px; display: flex; align-items: center; gap:8px;
}

.modal input[type=text] {
  font-size: 17px;
  border: 1.5px solid #bcdffb;
  border-radius: 7px;
  padding: 8px 12px;
  outline: none;
  width: 100%;
  transition: border .18s, box-shadow .17s;
  margin-bottom: 6px;
}

.modal input[type=text]:focus {
  border: 1.5px solid #1976d2;
  box-shadow: 0 2px 8px #4285f41a;
}

.modal-actions {
  display: flex; gap: 16px; justify-content: flex-end;
}

/* TOAST STYLES */
#toast-container {
  position: fixed;
  top: 21px; right: 38px; z-index: 300;
  display: flex; flex-direction: column; gap: 12px;
}

.toast {
  display: flex; align-items: center;
  min-width: 210px; max-width: 330px;
  padding: 13px 24px 13px 17px;
  border-radius: 14px;
  background: #1976d2;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 15px #1976d244;
  animation: toast-in .25s;
  opacity: 0.97;
}

.toast-info   { background: #1976d2; }
.toast-success{ background: #34a853;}
.toast-warning{ background: #ff9800;}
.toast-error  { background: #d32f2f;}
.toast .mdi   { font-size: 20px; margin-right: 13px;}
.toast.fade-out { animation: toast-out .5s; opacity: 0;}

@keyframes toast-in { 
  from{transform: translateY(-32px); opacity:0;} 
  to{transform:translateY(0); opacity:0.97;}
}

@keyframes toast-out { 
  to {transform: translateY(-24px); opacity:0;} 
}

/* PROGRESS BAR */
.progress-wrap {
  height: 36px;
  min-width: 220px;
  background: #e7e9ee;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 2px 8px #4285f414;
  display: none;
  align-items: center;
  position: relative;
  margin-left: 18px; margin-right: 10px;
  flex: 1 1 240px;
  z-index: 99;
}

.progress-bar {
  height: 100%;
  width: 0%;
  border-radius: 18px;
  position: relative;
  background: linear-gradient(90deg, #34a853 0%, #4285f4 75%);
  transition: width 0.23s cubic-bezier(.44,0,.48,1.3);
}

.progress-bar::after {
  content: "";
  display: block;
  position: absolute; left: 0; top: 0; width: 100%; height: 100%;
  pointer-events: none;
  background: repeating-linear-gradient(
      45deg,
      #fff4 0 10px,
      #fff0 10px 20px
  );
  opacity: 0.20;
  animation: progressMove 1.2s linear infinite;
}

@keyframes progressMove {
  0% { background-position-x: 0; }
  100% { background-position-x: 60px; }
}

.progress-text {
  position: absolute;
  left: 50%; top: 50%;
  transform: translate(-50%, -50%);
  font-size: 17px;
  color: #1976d2;
  font-weight: 700;
  text-shadow: 0 2px 7px #fff, 0 1px 1px #fff9;
  white-space: nowrap;
  letter-spacing: .01em;
}

/* STORAGE BAR */
.storage-bar-wrap { 
  margin: 5px 0 3px 0;
}

.storage-bar-bg { 
  width: 100%; background:#e4e6ea; height:13px; 
  border-radius:8px; overflow:hidden; 
}

.storage-bar-used {
  height: 100%; 
  background: linear-gradient(90deg, #4285f4, #34a853);
  border-radius:8px 0 0 8px; 
  box-shadow: 0 1px 3px #4285f47a;
}

/* EMPTY NOTE */
.empty-note {
  color: #b0b3b9;
  text-align: center;
  margin: 38px 0 18px 0;
  font-size: 18px;
}

/* PREVIEW MODAL STYLES */
.preview-modal {
  z-index: 1000;
  display: none; /* Hidden by default */
}

.preview-modal-content {
  background: #fff;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  width: 900px;
  height: 700px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 40px rgba(0,0,0,0.3);
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.preview-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.btn-preview-action,
.btn-preview-close {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  font-size: 18px;
  transition: all 0.2s;
}

.btn-preview-action:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-preview-close:hover {
  background: #ffebee;
  color: #d32f2f;
}

.preview-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #fafafa;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #666;
  font-size: 16px;
}

.preview-loading .mdi {
  font-size: 32px;
  margin-bottom: 8px;
}

.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #666;
  text-align: center;
  padding: 40px;
}

.preview-error .mdi {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ff5722;
}

/* Image Preview */
.preview-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  opacity: 0;
  transition: opacity 0.3s;
}

/* Video Preview */
.preview-video-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.preview-video {
  max-width: 100%;
  max-height: 100%;
}

/* Audio Preview */
.preview-audio-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.audio-icon {
  font-size: 80px;
  margin-bottom: 30px;
  opacity: 0.8;
}

.preview-audio {
  width: 400px;
  max-width: 80%;
}

/* PDF Preview */
.preview-pdf-container {
  width: 100%;
  height: 100%;
}

.preview-pdf {
  width: 100%;
  height: 100%;
  border: none;
}

/* Text Preview */
.preview-text-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow: auto;
  background: #fff;
}

.preview-text {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
  color: #333;
}

/* Code Preview */
.preview-code-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #2d2d2d;
  color: #fff;
  font-size: 14px;
}

.code-language {
  background: #007acc;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
}

.code-lines {
  color: #888;
}

.preview-code {
  flex: 1;
  margin: 0;
  padding: 20px;
  overflow: auto;
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
}

/* Unsupported Preview */
.preview-unsupported {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #666;
  padding: 60px 40px;
}

.preview-file-icon {
  font-size: 80px;
  margin-bottom: 20px;
  color: #ccc;
}

.preview-unsupported h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
}

.preview-unsupported p {
  margin: 0 0 30px 0;
  font-size: 16px;
}

/* Preview Footer */
.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.preview-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.preview-controls {
  display: flex;
  gap: 8px;
}

.btn-preview-nav {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  font-size: 18px;
  transition: all 0.2s;
}

.btn-preview-nav:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.btn-preview-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* ===== MOBILE-FIRST RESPONSIVE DESIGN ===== */

/* Base mobile styles (320px+) - Touch-friendly design */
@media (max-width: 480px) {
  .main-content {
    max-width: 100%;
    padding: 12px;
    margin: 8px;
    border-radius: 8px;
  }

  /* Mobile header */
  .header {
    padding: 12px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header h1 {
    font-size: 18px;
    margin: 0;
    text-align: center;
  }

  /* Mobile toolbar - Touch-friendly */
  .toolbar {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 12px 0;
    align-items: stretch;
  }

  .toolbar-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .toolbar-primary {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .toolbar-secondary {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .toolbar .upload-label,
  .toolbar .btn {
    font-size: 14px;
    padding: 14px 8px;
    min-height: 48px; /* Touch-friendly minimum 44px+ */
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-align: center;
    white-space: nowrap;
  }

  .toolbar .mdi {
    font-size: 18px;
  }

  /* Mobile search */
  .search-container {
    margin: 8px 0;
    max-width: none;
  }

  .search-input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 14px 16px;
    min-height: 48px;
    border-radius: 8px;
  }

  /* Mobile file table - Simplified */
  .file-list {
    font-size: 14px;
  }

  .file-list th,
  .file-list td {
    padding: 12px 8px;
  }

  /* Hide columns on mobile */
  .file-list th.col-time,
  .file-list th.col-action,
  .file-list th.col-checkbox,
  .file-list td.col-time,
  .file-list td.col-action,
  .file-list td.col-checkbox {
    display: none;
  }

  .col-name {
    width: 70%;
  }

  .col-size {
    width: 30%;
    text-align: right;
  }

  /* Mobile file names */
  .file-link,
  .folder-link {
    font-size: 14px;
    line-height: 1.4;
    word-break: break-word;
    hyphens: auto;
  }

  /* Mobile breadcrumbs */
  .breadcrumbs {
    font-size: 13px;
    padding: 8px 0;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .breadcrumbs::-webkit-scrollbar {
    display: none;
  }

  .breadcrumb-link,
  .breadcrumb-current {
    padding: 6px 8px;
    border-radius: 4px;
    min-height: 32px;
    display: inline-flex;
    align-items: center;
  }

  /* Mobile empty state */
  .empty-note {
    padding: 40px 20px;
    font-size: 16px;
    text-align: center;
  }

  /* Mobile sort controls */
  .sort-container {
    margin: 8px 0;
  }

  .btn-sort {
    min-width: 120px;
    padding: 12px 16px;
    font-size: 14px;
    min-height: 44px;
  }

  .sort-menu {
    min-width: 200px;
    right: 8px;
    left: 8px;
  }

  /* Mobile view toggle */
  .view-toggle-container {
    grid-column: span 2;
    display: flex;
    justify-content: center;
  }

  .btn-view-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
  }

  /* Mobile theme toggle */
  .theme-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
  }
}

/* Tablet styles (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .main-content {
    max-width: 100%;
    padding: 16px;
    margin: 12px;
    border-radius: 12px;
  }

  .header {
    padding: 16px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .header h1 {
    font-size: 22px;
  }

  .toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 16px 0;
  }

  .toolbar .upload-label,
  .toolbar .btn {
    font-size: 14px;
    padding: 10px 16px;
    min-height: 44px;
  }

  /* Show time column on tablet */
  .file-list th.col-time,
  .file-list td.col-time {
    display: table-cell;
  }

  .col-name {
    width: 45%;
  }

  .col-time {
    width: 25%;
  }

  .col-size {
    width: 20%;
  }

  .col-action {
    width: 10%;
    display: table-cell;
  }

  /* Tablet action buttons */
  .action-group {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 4px;
    flex-wrap: wrap;
  }

  .btn-action {
    font-size: 16px;
    padding: 8px;
    min-width: 36px;
    min-height: 36px;
  }
}

/* Desktop styles (769px+) */
@media (min-width: 769px) {
  .main-content {
    max-width: 1200px;
    padding: 20px 24px 24px 24px;
    margin: 15px auto;
    border-radius: 16px;
  }

  .header {
    padding: 16px 20px;
    flex-direction: row;
  }

  .header h1 {
    font-size: 24px;
  }

  .toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 16px 0;
  }

  .toolbar .upload-label,
  .toolbar .btn {
    font-size: 14px;
    padding: 10px 16px;
    min-height: 40px;
    width: auto;
  }

  /* Show all columns on desktop */
  .file-list th.col-time,
  .file-list th.col-action,
  .file-list th.col-checkbox,
  .file-list td.col-time,
  .file-list td.col-action,
  .file-list td.col-checkbox {
    display: table-cell;
  }

  .col-name {
    width: auto;
  }

  .col-time {
    width: 180px;
  }

  .col-size {
    width: 120px;
  }

  .col-action {
    width: 160px;
  }

  .action-group {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 8px;
  }

  .btn-action {
    font-size: 14px;
    padding: 6px 8px;
    min-width: 32px;
    min-height: 32px;
  }
}

/* ===== RECYCLE BIN STYLES ===== */

/* Recycle Bin Button */
.btn-recycle {
  background: #d93025 !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #d9302528;
}

.btn-recycle:hover {
  background: #b52d20 !important;
  color: #fff !important;
  box-shadow: 0 3px 12px #d9302540;
}

.btn-recycle .mdi {
  color: #fff !important;
}

/* Recycle Bin Info Banner */
.recycle-info-banner {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #856404;
  font-size: 14px;
}

.recycle-info-banner .mdi {
  color: #f39c12;
  font-size: 18px;
}

/* Recycle Bin Action Buttons */
.btn-restore {
  color: #4caf50;
}

.btn-restore:hover {
  background: #e8f5e8;
  color: #2e7d32;
  box-shadow: 0 2px 8px #4caf5041;
}

.btn-permanent-delete {
  color: #d32f2f;
}

.btn-permanent-delete:hover {
  background: #ffebee;
  color: #b71c1c;
  box-shadow: 0 2px 8px #d32f2f41;
}

/* Danger Button Styles */
.btn-danger {
  background: #d32f2f !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #d32f2f28;
}

.btn-danger:hover {
  background: #b71c1c !important;
  color: #fff !important;
  box-shadow: 0 3px 12px #d32f2f40;
}

.btn-danger .mdi {
  color: #fff !important;
}

/* Refresh Button */
.btn-refresh {
  background: #6c757d !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #6c757d28;
}

.btn-refresh:hover {
  background: #5a6268 !important;
  color: #fff !important;
  box-shadow: 0 3px 12px #6c757d40;
}

.btn-refresh .mdi {
  color: #fff !important;
}

/* Back Button */
.btn-back {
  background: #6c757d !important;
  color: #fff !important;
  border: none !important;
  box-shadow: 0 2px 8px #6c757d28;
}

.btn-back:hover {
  background: #5a6268 !important;
  color: #fff !important;
  box-shadow: 0 3px 12px #6c757d40;
}

.btn-back .mdi {
  color: #fff !important;
}

/* Empty State for Recycle Bin */
.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

#emptyTrashNote {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

#emptyTrashNote h3 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

#emptyTrashNote p {
  margin-bottom: 24px;
  font-size: 16px;
  color: #666;
}

/* Modal Overlay for Confirmations */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: modalFadeIn 0.2s ease;
}

.modal-content {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #f5f5f5;
  color: #333;
}

.modal-body {
  padding: 20px 24px;
}

.modal-body p {
  margin: 0;
  color: #555;
  font-size: 16px;
  line-height: 1.5;
}

.modal-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #e9ecef;
  color: #495057;
  border-color: #adb5bd;
}

/* Trashed file styling */
.file-name {
  opacity: 0.8;
  font-style: italic;
}

/* Mobile responsive for recycle bin */
@media (max-width: 768px) {
  .recycle-info-banner {
    padding: 10px 12px;
    font-size: 13px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .modal-footer {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .btn-secondary,
  .btn-danger {
    width: 100%;
    text-align: center;
  }
}

/* ===== CONTEXT MENU STYLES ===== */

.context-menu {
  position: fixed;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--shadow-heavy);
  padding: 4px 0;
  min-width: 200px;
  z-index: 1000;
  user-select: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  transform: scale(0.95);
  opacity: 0;
}

.context-menu.show {
  opacity: 1;
  transform: scale(1);
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--text-primary);
  font-size: 14px;
  gap: 12px;
}

.context-menu-item:hover {
  background: var(--bg-hover);
}

.context-menu-item:active {
  background: var(--bg-selected);
}

.context-menu-item .mdi {
  font-size: 16px;
  color: var(--text-secondary);
  width: 16px;
  text-align: center;
  transition: color 0.2s ease;
}

.context-menu-item:hover .mdi {
  color: var(--text-accent);
}

.context-menu-item span:nth-child(2) {
  flex: 1;
  font-weight: 500;
}

.context-shortcut {
  font-size: 12px;
  color: var(--text-muted);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid var(--border-primary);
  font-family: 'Courier New', monospace;
  transition: all 0.2s ease;
}

.context-menu-item:hover .context-shortcut {
  background: var(--bg-accent);
  color: var(--text-accent);
  border-color: var(--border-accent);
}

.context-menu-separator {
  height: 1px;
  background: var(--border-primary);
  margin: 4px 8px;
  transition: background-color 0.3s ease;
}

.context-menu-danger {
  color: var(--error);
}

.context-menu-danger .mdi {
  color: var(--error);
}

.context-menu-danger:hover {
  background: #ffebee;
  color: #b71c1c;
}

[data-theme="dark"] .context-menu-danger:hover {
  background: #2d1b1b;
  color: #f44336;
}

/* Context menu animations */
@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.context-menu.animate-in {
  animation: contextMenuSlideIn 0.2s ease-out;
}

/* ===== TOUCH-FRIENDLY INTERFACE IMPROVEMENTS ===== */

/* Touch-friendly context menu */
@media (max-width: 768px) {
  .context-menu {
    min-width: 200px;
    font-size: 16px;
    border-radius: 12px;
  }

  .context-menu-item {
    padding: 16px 20px; /* Increased for better touch */
    font-size: 16px;
    min-height: 48px; /* Touch-friendly minimum */
    display: flex;
    align-items: center;
  }

  .context-menu-item .mdi {
    font-size: 20px;
    margin-right: 12px;
  }

  .context-shortcut {
    display: none; /* Hide shortcuts on mobile */
  }
}

/* Touch-friendly file actions */
@media (max-width: 768px) {
  .btn-action {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    margin: 2px;
    border-radius: 8px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Touch feedback */
  .btn-action:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* File grid touch improvements */
  .file-grid-item {
    min-height: 160px;
    padding: 16px;
    border-radius: 12px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  .file-grid-item:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .file-grid-actions {
    top: 12px;
    right: 12px;
    gap: 8px;
  }

  .file-grid-action {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
    border-radius: 8px;
    font-size: 18px;
  }

  /* Table row touch improvements */
  .file-list tr {
    min-height: 56px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  .file-list tr:active {
    background-color: var(--bg-selected);
    transition: background-color 0.1s ease;
  }

  .file-list td {
    padding: 16px 12px;
    vertical-align: middle;
  }

  /* Upload button touch improvements */
  .upload-label {
    min-height: 48px;
    padding: 14px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  .upload-label:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Button touch improvements */
  .btn {
    min-height: 48px;
    padding: 14px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation; /* Prevent double-tap zoom */
  }

  .btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Search input touch improvements */
  .search-input {
    min-height: 48px;
    padding: 14px 16px;
    border-radius: 8px;
    font-size: 16px; /* Prevent zoom on iOS */
    -webkit-appearance: none;
    touch-action: manipulation;
  }

  /* Sort button touch improvements */
  .btn-sort {
    min-height: 48px;
    padding: 14px 16px;
    border-radius: 8px;
    touch-action: manipulation;
  }

  .btn-sort:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Theme toggle touch improvements */
  .theme-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    border-radius: 8px;
    touch-action: manipulation;
  }

  .theme-toggle:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* View toggle touch improvements */
  .btn-view-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    border-radius: 8px;
    touch-action: manipulation;
  }

  .btn-view-toggle:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Breadcrumb touch improvements */
  .breadcrumb-link {
    min-height: 44px;
    padding: 10px 12px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    touch-action: manipulation;
  }

  .breadcrumb-link:active {
    background-color: var(--bg-selected);
    transition: background-color 0.1s ease;
  }

  /* Modal button touch improvements */
  .btn-secondary,
  .btn-danger {
    min-height: 48px;
    padding: 14px 20px;
    border-radius: 8px;
    font-size: 16px;
    touch-action: manipulation;
  }

  .btn-secondary:active,
  .btn-danger:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Checkbox touch improvements */
  .file-checkbox {
    min-width: 20px;
    min-height: 20px;
    margin: 12px;
    cursor: pointer;
  }

  /* Link touch improvements */
  .file-link,
  .folder-link {
    padding: 8px 4px;
    border-radius: 4px;
    display: inline-block;
    min-height: 32px;
    line-height: 1.4;
    touch-action: manipulation;
  }

  .file-link:active,
  .folder-link:active {
    background-color: var(--bg-hover);
    transition: background-color 0.1s ease;
  }
}

/* ===== MOBILE FILE MANAGEMENT IMPROVEMENTS ===== */

/* Mobile action menu (slide-up) */
@media (max-width: 768px) {
  .mobile-action-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
    box-shadow: 0 -4px 20px var(--shadow-heavy);
  }

  .mobile-action-menu.show {
    transform: translateY(0);
  }

  .mobile-action-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-primary);
  }

  .mobile-action-menu-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
  }

  .mobile-action-menu-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    min-width: 32px;
    min-height: 32px;
  }

  .mobile-action-menu-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .mobile-action-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 16px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 80px;
    touch-action: manipulation;
  }

  .mobile-action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-accent);
  }

  .mobile-action-btn:active {
    transform: scale(0.98);
    background: var(--bg-selected);
  }

  .mobile-action-btn .mdi {
    font-size: 24px;
    color: var(--text-accent);
  }

  .mobile-action-btn span {
    font-size: 12px;
    color: var(--text-primary);
    text-align: center;
    font-weight: 500;
  }

  /* Mobile file table improvements */
  .file-list {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--shadow-light);
  }

  .file-list thead {
    background: var(--bg-tertiary);
  }

  .file-list th {
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    padding: 16px 12px;
    border-bottom: 1px solid var(--border-primary);
  }

  .file-list tbody tr {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    transition: background-color 0.2s ease;
  }

  .file-list tbody tr:hover {
    background: var(--bg-hover);
  }

  .file-list tbody tr:last-child {
    border-bottom: none;
  }

  .file-list td {
    padding: 16px 12px;
    vertical-align: middle;
  }

  /* Mobile file name display */
  .mobile-file-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .mobile-file-icon {
    font-size: 24px;
    min-width: 24px;
  }

  .mobile-file-details {
    flex: 1;
    min-width: 0;
  }

  .mobile-file-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
    word-break: break-word;
    line-height: 1.3;
  }

  .mobile-file-meta {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .mobile-file-size,
  .mobile-file-date {
    white-space: nowrap;
  }

  /* Mobile action trigger */
  .mobile-action-trigger {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-action-trigger:hover {
    background: var(--bg-hover);
    border-color: var(--border-accent);
  }

  .mobile-action-trigger:active {
    transform: scale(0.95);
  }

  .mobile-action-trigger .mdi {
    font-size: 18px;
    color: var(--text-secondary);
  }

  /* Mobile file selection */
  .mobile-select-mode .file-list tbody tr {
    cursor: pointer;
  }

  .mobile-select-mode .file-list tbody tr.selected {
    background: var(--bg-selected);
    border-color: var(--border-accent);
  }

  .mobile-select-mode .file-checkbox {
    display: block;
  }

  /* Mobile bulk actions bar */
  .mobile-bulk-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--text-accent);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 999;
  }

  .mobile-bulk-actions.show {
    transform: translateY(0);
  }

  .mobile-bulk-actions-info {
    font-size: 14px;
    font-weight: 500;
  }

  .mobile-bulk-actions-buttons {
    display: flex;
    gap: 12px;
  }

  .mobile-bulk-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
  }

  .mobile-bulk-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .mobile-bulk-btn:active {
    transform: scale(0.95);
  }
}

/* ===== ENHANCED MOBILE NAVIGATION ===== */

/* Mobile header improvements */
@media (max-width: 768px) {
  .header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: 0 2px 8px var(--shadow-light);
  }

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    min-height: 56px;
  }

  .header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    flex: 1;
    text-align: center;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* Mobile breadcrumbs */
  .breadcrumbs {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: 12px 16px;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
  }

  .breadcrumbs::-webkit-scrollbar {
    display: none;
  }

  .breadcrumb-container {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: max-content;
  }

  .breadcrumb-link,
  .breadcrumb-current {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: var(--text-primary);
    text-decoration: none;
    white-space: nowrap;
    min-height: 36px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    touch-action: manipulation;
  }

  .breadcrumb-link:hover {
    background: var(--bg-hover);
    border-color: var(--border-accent);
  }

  .breadcrumb-link:active {
    transform: scale(0.98);
    background: var(--bg-selected);
  }

  .breadcrumb-current {
    background: var(--text-accent);
    color: white;
    border-color: var(--text-accent);
    font-weight: 500;
  }

  .breadcrumb-separator {
    color: var(--text-muted);
    font-size: 12px;
    margin: 0 4px;
  }

  .breadcrumb-icon {
    font-size: 14px;
  }

  /* Mobile search improvements */
  .search-container {
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
  }

  .search-box {
    position: relative;
    width: 100%;
  }

  .search-input {
    width: 100%;
    padding: 14px 16px 14px 48px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 16px; /* Prevent zoom on iOS */
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    min-height: 48px;
    box-sizing: border-box;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 2px var(--border-accent-light);
  }

  .search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 18px;
    pointer-events: none;
  }

  .search-clear {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .search-clear:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
  }

  .search-clear:active {
    transform: translateY(-50%) scale(0.95);
  }

  /* Mobile toolbar in navigation */
  .mobile-nav-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-primary);
  }

  .mobile-nav-left {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .mobile-nav-right {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .mobile-nav-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
    display: flex;
    align-items: center;
    gap: 6px;
    touch-action: manipulation;
  }

  .mobile-nav-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-accent);
  }

  .mobile-nav-btn:active {
    transform: scale(0.98);
    background: var(--bg-selected);
  }

  .mobile-nav-btn .mdi {
    font-size: 16px;
  }

  /* Mobile back button */
  .mobile-back-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 10px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  .mobile-back-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-accent);
  }

  .mobile-back-btn:active {
    transform: scale(0.95);
    background: var(--bg-selected);
  }

  .mobile-back-btn .mdi {
    font-size: 20px;
  }

  /* Mobile storage info */
  .storage-info {
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
  }

  .storage-bar {
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
    margin: 8px 0;
  }

  .storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--text-accent), var(--text-accent-light));
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .storage-text {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
    margin-top: 4px;
  }
}

/* ===== MOBILE VISUAL DESIGN OPTIMIZATION ===== */

/* Mobile typography improvements */
@media (max-width: 768px) {
  /* Base typography */
  body {
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.3;
    font-weight: 600;
    margin-bottom: 8px;
  }

  h1 {
    font-size: 20px;
  }

  h2 {
    font-size: 18px;
  }

  h3 {
    font-size: 16px;
  }

  /* Improved contrast for mobile */
  .file-link,
  .folder-link {
    color: var(--text-primary);
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .file-link:hover,
  .folder-link:hover {
    color: var(--text-accent);
  }

  /* Better text contrast */
  .text-muted {
    color: var(--text-secondary);
    opacity: 0.8;
  }

  .text-small {
    font-size: 12px;
    line-height: 1.4;
  }

  /* Mobile-optimized animations */
  .mobile-fade-in {
    animation: mobileFadeIn 0.3s ease-out;
  }

  .mobile-slide-up {
    animation: mobileSlideUp 0.3s ease-out;
  }

  .mobile-scale-in {
    animation: mobileScaleIn 0.2s ease-out;
  }

  @keyframes mobileFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes mobileSlideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes mobileScaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mobile performance optimizations */
  .mobile-optimized {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .btn,
    .upload-label {
      border-width: 2px;
      font-weight: 600;
    }

    .file-list {
      border: 2px solid var(--border-primary);
    }

    .file-list th,
    .file-list td {
      border-bottom: 1px solid var(--border-primary);
    }
  }

  /* Dark theme mobile optimizations */
  [data-theme="dark"] {
    /* Better contrast in dark mode */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2a2a2a;
    --border-primary: #333333;
    --border-accent: #4a9eff;
  }

  /* Mobile focus styles */
  .mobile-focus:focus,
  .btn:focus,
  .upload-label:focus,
  .search-input:focus {
    outline: 2px solid var(--text-accent);
    outline-offset: 2px;
  }

  /* Mobile loading states */
  .mobile-loading {
    position: relative;
    overflow: hidden;
  }

  .mobile-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: mobileShimmer 1.5s infinite;
  }

  @keyframes mobileShimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  /* Mobile error states */
  .mobile-error {
    background: #ffebee;
    border: 1px solid #ffcdd2;
    color: #c62828;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin: 8px 0;
  }

  [data-theme="dark"] .mobile-error {
    background: #2d1b1b;
    border-color: #5d2f2f;
    color: #f44336;
  }

  /* Mobile success states */
  .mobile-success {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    color: #2e7d32;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin: 8px 0;
  }

  [data-theme="dark"] .mobile-success {
    background: #1b2d1b;
    border-color: #2f5d2f;
    color: #4caf50;
  }

  /* Mobile spacing utilities */
  .mobile-mt-1 { margin-top: 4px; }
  .mobile-mt-2 { margin-top: 8px; }
  .mobile-mt-3 { margin-top: 12px; }
  .mobile-mt-4 { margin-top: 16px; }

  .mobile-mb-1 { margin-bottom: 4px; }
  .mobile-mb-2 { margin-bottom: 8px; }
  .mobile-mb-3 { margin-bottom: 12px; }
  .mobile-mb-4 { margin-bottom: 16px; }

  .mobile-p-1 { padding: 4px; }
  .mobile-p-2 { padding: 8px; }
  .mobile-p-3 { padding: 12px; }
  .mobile-p-4 { padding: 16px; }

  /* Mobile text utilities */
  .mobile-text-center { text-align: center; }
  .mobile-text-left { text-align: left; }
  .mobile-text-right { text-align: right; }

  .mobile-font-bold { font-weight: 600; }
  .mobile-font-medium { font-weight: 500; }
  .mobile-font-normal { font-weight: 400; }

  /* Mobile display utilities */
  .mobile-hidden { display: none !important; }
  .mobile-block { display: block !important; }
  .mobile-flex { display: flex !important; }
  .mobile-inline-flex { display: inline-flex !important; }
}

/* ===== LOADING SKELETON STYLES ===== */

.skeleton {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-hover) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* File List Table Skeleton */
.skeleton-table {
  width: 100%;
  border-collapse: collapse;
}

.skeleton-table-row {
  height: 60px;
  border-bottom: 1px solid var(--border-primary);
}

.skeleton-table-cell {
  padding: 12px 16px;
  vertical-align: middle;
}

.skeleton-file-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: inline-block;
  margin-right: 12px;
}

.skeleton-file-name {
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  vertical-align: middle;
}

.skeleton-file-name.short {
  width: 120px;
}

.skeleton-file-name.medium {
  width: 180px;
}

.skeleton-file-name.long {
  width: 240px;
}

.skeleton-file-date {
  width: 100px;
  height: 14px;
  border-radius: 4px;
}

.skeleton-file-size {
  width: 60px;
  height: 14px;
  border-radius: 4px;
}

.skeleton-file-actions {
  display: flex;
  gap: 8px;
}

.skeleton-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

/* File Grid Skeleton */
.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 8px 0;
}

.skeleton-grid-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.skeleton-grid-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-bottom: 8px;
}

.skeleton-grid-name {
  width: 80%;
  height: 14px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.skeleton-grid-meta {
  width: 60%;
  height: 12px;
  border-radius: 4px;
  margin-top: auto;
}

/* Search Results Skeleton */
.skeleton-search-container {
  padding: 16px 0;
}

.skeleton-search-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

.skeleton-search-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.skeleton-search-text {
  width: 200px;
  height: 16px;
  border-radius: 4px;
}

/* Breadcrumb Skeleton */
.skeleton-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 8px 0;
}

.skeleton-breadcrumb-item {
  height: 16px;
  border-radius: 4px;
}

.skeleton-breadcrumb-item.home {
  width: 80px;
}

.skeleton-breadcrumb-item.folder {
  width: 100px;
}

.skeleton-breadcrumb-separator {
  width: 8px;
  height: 8px;
  border-radius: 2px;
}

/* Storage Bar Skeleton */
.skeleton-storage {
  height: 20px;
  border-radius: 10px;
  margin: 15px 0 18px 0;
  width: 100%;
  max-width: 400px;
}

/* Responsive skeleton adjustments */
@media (max-width: 768px) {
  .skeleton-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }

  .skeleton-grid-item {
    min-height: 120px;
    padding: 12px 8px;
  }

  .skeleton-grid-icon {
    width: 36px;
    height: 36px;
  }

  .skeleton-file-name.long {
    width: 160px;
  }

  .skeleton-file-name.medium {
    width: 120px;
  }
}

@media (max-width: 480px) {
  .skeleton-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .skeleton-grid-item {
    min-height: 100px;
    padding: 8px 6px;
  }

  .skeleton-grid-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 6px;
  }
}