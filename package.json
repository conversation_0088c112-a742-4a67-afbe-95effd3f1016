{"name": "mini-google-drive", "version": "2.0.0", "description": "A modern file management system using Google Drive API", "main": "server/index.js", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "build": "echo 'No build step required'", "vercel-build": "echo 'Vercel build complete'", "check-deployment": "node scripts/check-deployment.js"}, "dependencies": {"express": "^5.1.0", "googleapis": "^150.0.1", "multer": "^2.0.1", "dotenv": "^16.4.5", "express-session": "^1.18.0", "express-rate-limit": "^7.2.0", "cors": "^2.8.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.1.0"}}