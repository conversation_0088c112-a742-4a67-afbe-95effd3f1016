<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="theme-color" content="#4a9eff">
  <title>Mini Google Drive - File Manager</title>
  <meta name="description" content="Modern file management system using Google Drive API">
  
  <!-- Icons & Fonts -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css">
  <link rel="icon" href="https://ssl.gstatic.com/images/branding/product/1x/drive_2020q4_48dp.png">
  
  <!-- Styles -->
  <link rel="stylesheet" href="css/main.css">
</head>
<body>
  <!-- Header -->
  <div class="drive-header">
    <div class="header-left">
      <img src="https://ssl.gstatic.com/images/branding/product/1x/drive_2020q4_48dp.png"
           alt="Drive"
           style="width:32px;height:32px;margin-right:13px;">
      <span class="header-title">Mini Google Drive</span>
    </div>

    <div class="header-right">
      <button class="theme-toggle" id="themeToggle" title="Chuyển đổi chế độ sáng/tối">
        <span class="mdi mdi-weather-night" id="themeIcon"></span>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Toolbar -->
    <div class="toolbar">
      <!-- Primary Actions Group -->
      <div class="toolbar-group toolbar-primary">
        <label class="upload-label upload-file" title="Tải lên tệp">
          <span class="mdi mdi-upload"></span> Tải lên tệp
          <input type="file" id="fileInput" multiple>
        </label>

        <label class="upload-label upload-folder" title="Tải lên thư mục">
          <span class="mdi mdi-folder-upload"></span> Tải lên thư mục
          <input type="file" id="folderInput" webkitdirectory directory multiple>
        </label>

        <button class="btn btn-create" id="createFolderBtn">
          <span class="mdi mdi-folder-plus-outline"></span> Tạo thư mục
        </button>
      </div>

      <!-- Secondary Actions Group -->
      <div class="toolbar-group toolbar-secondary">
        <button class="btn btn-recycle" id="recycleBinBtn" title="Thùng rác">
          <span class="mdi mdi-delete"></span> Thùng rác
        </button>

        <div class="view-toggle-container">
          <button class="btn btn-view-toggle" id="viewToggleBtn" title="Chuyển đổi chế độ xem">
            <span class="mdi mdi-view-list" id="viewToggleIcon"></span>
          </button>
        </div>

        <!-- Search will be added here by searchManager -->
      </div>

      <!-- Progress Bar -->
      <div class="progress-wrap" id="progressWrap">
        <div class="progress-bar" id="progressBar"></div>
        <span class="progress-text" id="progressText"></span>
      </div>
    </div>

    <!-- Storage Info -->
    <div id="storageBar" style="margin: 15px 0 18px 0; font-size:15px;"></div>

    <!-- Breadcrumbs -->
    <div class="breadcrumbs" id="breadcrumbs"></div>

    <!-- Drag & Drop Area -->
    <div class="drop-area" id="dropArea">
      Kéo & thả tệp/thư mục vào đây để tải lên...
    </div>

    <!-- File List Table -->
    <div class="table-wrapper">
      <table class="file-list" id="fileTable">
        <thead>
          <tr>
            <th class="col-name">Tên</th>
            <th class="col-time">Ngày cập nhật</th>
            <th class="col-size">Dung lượng</th>
            <th class="col-action">Tác vụ</th>
          </tr>
        </thead>
        <tbody id="fileListBody"></tbody>
      </table>
    </div>

    <!-- File Grid View -->
    <div class="file-grid" id="fileGrid" style="display: none;">
      <!-- Grid items will be populated by JavaScript -->
    </div>

    <!-- Empty State -->
    <div class="empty-note" id="emptyNote" style="display: none;">
      Chưa có tệp hay thư mục nào
    </div>
  </div>

  <!-- Toast Container -->
  <div id="toast-container"></div>

  <!-- Context Menu -->
  <div class="context-menu" id="contextMenu" style="display: none;">
    <div class="context-menu-item" data-action="preview">
      <span class="mdi mdi-eye"></span>
      <span>Xem trước</span>
      <span class="context-shortcut">Space</span>
    </div>
    <div class="context-menu-item" data-action="download">
      <span class="mdi mdi-download"></span>
      <span>Tải xuống</span>
      <span class="context-shortcut">Ctrl+D</span>
    </div>
    <div class="context-menu-separator"></div>
    <div class="context-menu-item" data-action="rename">
      <span class="mdi mdi-pencil"></span>
      <span>Đổi tên</span>
      <span class="context-shortcut">F2</span>
    </div>
    <div class="context-menu-item" data-action="copy-link">
      <span class="mdi mdi-link"></span>
      <span>Sao chép liên kết</span>
      <span class="context-shortcut">Ctrl+L</span>
    </div>
    <div class="context-menu-separator"></div>
    <div class="context-menu-item context-menu-danger" data-action="delete">
      <span class="mdi mdi-delete"></span>
      <span>Xóa</span>
      <span class="context-shortcut">Delete</span>
    </div>
  </div>

  <!-- Mobile Action Menu -->
  <div class="mobile-action-menu" id="mobileActionMenu" style="display: none;">
    <div class="mobile-action-menu-header">
      <div class="mobile-action-menu-title" id="mobileActionTitle">Tác vụ file</div>
      <button class="mobile-action-menu-close" id="mobileActionClose">
        <span class="mdi mdi-close"></span>
      </button>
    </div>
    <div class="mobile-action-menu-actions" id="mobileActionButtons">
      <button class="mobile-action-btn" data-action="preview">
        <span class="mdi mdi-eye"></span>
        <span>Xem trước</span>
      </button>
      <button class="mobile-action-btn" data-action="download">
        <span class="mdi mdi-download"></span>
        <span>Tải xuống</span>
      </button>
      <button class="mobile-action-btn" data-action="rename">
        <span class="mdi mdi-pencil"></span>
        <span>Đổi tên</span>
      </button>
      <button class="mobile-action-btn" data-action="copy-link">
        <span class="mdi mdi-link"></span>
        <span>Sao chép link</span>
      </button>
      <button class="mobile-action-btn" data-action="delete">
        <span class="mdi mdi-delete"></span>
        <span>Xóa</span>
      </button>
    </div>
  </div>

  <!-- Mobile Bulk Actions Bar -->
  <div class="mobile-bulk-actions" id="mobileBulkActions" style="display: none;">
    <div class="mobile-bulk-actions-info" id="mobileBulkInfo">
      0 mục đã chọn
    </div>
    <div class="mobile-bulk-actions-buttons">
      <button class="mobile-bulk-btn" id="mobileBulkDownload">
        <span class="mdi mdi-download"></span> Tải
      </button>
      <button class="mobile-bulk-btn" id="mobileBulkDelete">
        <span class="mdi mdi-delete"></span> Xóa
      </button>
      <button class="mobile-bulk-btn" id="mobileBulkCancel">
        <span class="mdi mdi-close"></span> Hủy
      </button>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/uiUtils.js"></script>
  <script src="js/themeManager.js"></script>
  <script src="js/dialogManager.js"></script>
  <script src="js/viewManager.js"></script>
  <script src="js/contextMenuManager.js"></script>
  <script src="js/keyboardManager.js"></script>
  <script src="js/skeletonManager.js"></script>
  <script src="js/mobileActionManager.js"></script>
  <script src="js/fileManager.js"></script>
  <script src="js/uploadManager.js"></script>
  <script src="js/searchManager.js"></script>
  <script src="js/previewManager.js"></script>
  <script src="js/sortManager.js"></script>
  <script src="js/multiSelectManager.js"></script>
  <script src="js/app.js"></script>
</body>
</html> 